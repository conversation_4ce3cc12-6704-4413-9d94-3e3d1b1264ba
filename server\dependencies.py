"""
服务器依赖注入模块

提供 FastAPI 路由器使用的依赖注入函数
"""

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from server.auth.auth_middleware import get_current_user, get_db
from server.models.user_model import User
from src.database.manager import get_database_manager_dependency, UnifiedDatabaseManager


# ==================== 用户认证依赖 ====================

def get_required_user(current_user: User = Depends(get_current_user)) -> User:
    """
    获取必需的当前用户（需要登录）
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 如果用户未登录
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录才能访问此资源",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return current_user


def get_optional_user(current_user: User = Depends(get_current_user)) -> User:
    """
    获取可选的当前用户（不需要登录）
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前用户对象或 None
    """
    return current_user


# ==================== 数据库依赖 ====================

def get_database_session(db: Session = Depends(get_db)) -> Session:
    """
    获取数据库会话
    
    Args:
        db: 数据库会话
        
    Returns:
        Session: 数据库会话对象
    """
    return db


# ==================== 统一数据库管理器依赖 ====================

# 直接从 src.database.manager 导入
# 这样可以保持一致性
__all__ = [
    "get_required_user",
    "get_optional_user", 
    "get_database_session",
    "get_database_manager_dependency",  # 从 src.database.manager 导入
]
