# 生产环境环境变量模板
# 复制此文件为 .env.production 并填入实际值

# 环境设置
ENVIRONMENT=production

# 服务器主数据库配置
POSTGRES_HOST=your-postgres-host.example.com
POSTGRES_PORT=5432
POSTGRES_DB=yuxi_prod
POSTGRES_USER=yuxi_user
POSTGRES_PASSWORD=your-secure-password-here
POSTGRES_POOL_SIZE=20
POSTGRES_MAX_OVERFLOW=50

# LightRAG数据库配置
LIGHTRAG_POSTGRES_HOST=your-lightrag-postgres-host.example.com
LIGHTRAG_POSTGRES_PORT=5432
LIGHTRAG_POSTGRES_DB=lightrag_prod
LIGHTRAG_POSTGRES_USER=lightrag_user
LIGHTRAG_POSTGRES_PASSWORD=your-lightrag-password-here
LIGHTRAG_POSTGRES_POOL_SIZE=50
LIGHTRAG_POSTGRES_MAX_OVERFLOW=100

# Neo4j图数据库配置
NEO4J_URI=bolt://your-neo4j-host.example.com:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password-here
NEO4J_ENCRYPTED=true
NEO4J_TRUST=TRUST_SYSTEM_CA_SIGNED_CERTIFICATES
NEO4J_POOL_SIZE=100

# Milvus向量数据库配置
MILVUS_URI=http://your-milvus-host.example.com:19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_DB=default

# MinIO对象存储配置
MINIO_URI=http://your-minio-host.example.com:9000
MINIO_ACCESS_KEY=your-minio-access-key
MINIO_SECRET_KEY=your-minio-secret-key
MINIO_SECURE=true
MINIO_REGION=us-east-1

# 应用配置
SAVE_DIR=/app/data

# API密钥配置
SILICONFLOW_API_KEY=your-siliconflow-api-key
OPENAI_API_KEY=your-openai-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
ZHIPUAI_API_KEY=your-zhipuai-api-key
DASHSCOPE_API_KEY=your-dashscope-api-key
ARK_API_KEY=your-ark-api-key
TOGETHER_API_KEY=your-together-api-key
TAVILY_API_KEY=your-tavily-api-key

# 安全配置
JWT_SECRET_KEY=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# 监控配置
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true

# 日志配置
LOG_LEVEL=WARNING
LOG_FORMAT=json
LOG_FILE=/app/logs/application.log

# 性能配置
MAX_WORKERS=4
WORKER_TIMEOUT=30
KEEP_ALIVE=2

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份
BACKUP_RETENTION_DAYS=30