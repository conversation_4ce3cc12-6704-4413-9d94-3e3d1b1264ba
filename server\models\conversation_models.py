"""
企业会话管理数据模型

基于 PostgreSQL 的会话存储模型，支持企业级会话管理功能
"""

from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Index, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from server.models import Base


class ConversationThread(Base):
    """会话线程表 - 扩展版本"""
    __tablename__ = "conversation_threads"
    
    # 主键和基础信息
    id = Column(String(64), primary_key=True, index=True, comment="线程ID (UUID)")
    user_id = Column(String(64), index=True, nullable=False, comment="用户ID")
    agent_id = Column(String(64), index=True, nullable=False, comment="智能体ID")
    
    # 会话元数据
    title = Column(String(255), nullable=True, comment="会话标题")
    description = Column(Text, nullable=True, comment="会话描述")
    status = Column(String(20), default="active", comment="会话状态: active, archived, deleted")
    
    # 时间戳
    create_at = Column(DateTime, default=func.now(), comment="创建时间")
    update_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    last_message_at = Column(DateTime, nullable=True, comment="最后消息时间")
    
    # 扩展信息
    meta_data = Column(JSONB, nullable=True, comment="扩展元数据")
    tags = Column(JSONB, nullable=True, comment="标签列表")
    
    # 统计信息
    message_count = Column(Integer, default=0, comment="消息数量")
    
    # 关系
    messages = relationship("ConversationMessage", back_populates="thread", cascade="all, delete-orphan")
    checkpoints = relationship("ConversationCheckpoint", back_populates="thread", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_user_agent_status', 'user_id', 'agent_id', 'status'),
        Index('idx_user_update_time', 'user_id', 'update_at'),
        Index('idx_agent_create_time', 'agent_id', 'create_at'),
    )


class ConversationMessage(Base):
    """会话消息表"""
    __tablename__ = "conversation_messages"
    
    # 主键
    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()), comment="消息ID")
    thread_id = Column(String(64), ForeignKey('conversation_threads.id'), nullable=False, comment="线程ID")
    
    # 消息内容
    role = Column(String(20), nullable=False, comment="角色: human, ai, system, tool")
    content = Column(Text, nullable=False, comment="消息内容")
    content_type = Column(String(50), default="text", comment="内容类型: text, json, markdown")
    
    # 消息元数据
    message_index = Column(Integer, nullable=False, comment="消息在会话中的序号")
    parent_message_id = Column(String(64), nullable=True, comment="父消息ID (用于分支对话)")
    
    # 扩展信息
    meta_data = Column(JSONB, nullable=True, comment="消息元数据")
    tool_calls = Column(JSONB, nullable=True, comment="工具调用信息")
    attachments = Column(JSONB, nullable=True, comment="附件信息")
    
    # 时间戳
    create_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    # 关系
    thread = relationship("ConversationThread", back_populates="messages")
    
    # 索引
    __table_args__ = (
        Index('idx_thread_index', 'thread_id', 'message_index'),
        Index('idx_thread_role_time', 'thread_id', 'role', 'create_at'),
        Index('idx_thread_create_time', 'thread_id', 'create_at'),
    )


class ConversationCheckpoint(Base):
    """会话检查点表 - LangGraph 状态存储"""
    __tablename__ = "conversation_checkpoints"
    
    # 主键
    checkpoint_id = Column(String(64), primary_key=True, comment="检查点ID")
    thread_id = Column(String(64), ForeignKey('conversation_threads.id'), nullable=False, comment="线程ID")
    
    # 检查点信息
    checkpoint_ns = Column(String(100), default="", comment="检查点命名空间")
    parent_checkpoint_id = Column(String(64), nullable=True, comment="父检查点ID")
    
    # 状态数据
    checkpoint_data = Column(JSONB, nullable=False, comment="检查点状态数据")
    meta_data = Column(JSONB, nullable=True, comment="检查点元数据")
    
    # 时间戳
    create_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    # 关系
    thread = relationship("ConversationThread", back_populates="checkpoints")
    
    # 索引
    __table_args__ = (
        Index('idx_thread_checkpoint', 'thread_id', 'checkpoint_ns', 'create_at'),
        Index('idx_checkpoint_parent', 'parent_checkpoint_id'),
    )


class ConversationSummary(Base):
    """会话摘要表 - 用于长对话的摘要存储"""
    __tablename__ = "conversation_summaries"
    
    # 主键
    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()), comment="摘要ID")
    thread_id = Column(String(64), ForeignKey('conversation_threads.id'), nullable=False, comment="线程ID")
    
    # 摘要信息
    summary_type = Column(String(50), default="auto", comment="摘要类型: auto, manual, periodic")
    summary_content = Column(Text, nullable=False, comment="摘要内容")
    
    # 摘要范围
    start_message_index = Column(Integer, nullable=False, comment="摘要起始消息序号")
    end_message_index = Column(Integer, nullable=False, comment="摘要结束消息序号")
    
    # 时间戳
    create_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    # 索引
    __table_args__ = (
        Index('idx_thread_summary_range', 'thread_id', 'start_message_index', 'end_message_index'),
    )


class UserConversationPreference(Base):
    """用户会话偏好设置表"""
    __tablename__ = "user_conversation_preferences"
    
    # 主键
    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()), comment="偏好ID")
    user_id = Column(String(64), index=True, nullable=False, comment="用户ID")
    agent_id = Column(String(64), nullable=True, comment="智能体ID (为空表示全局设置)")
    
    # 偏好设置
    preferences = Column(JSONB, nullable=False, comment="偏好设置JSON")
    
    # 时间戳
    create_at = Column(DateTime, default=func.now(), comment="创建时间")
    update_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 索引
    __table_args__ = (
        Index('idx_user_agent_pref', 'user_id', 'agent_id'),
    )


class ConversationShare(Base):
    """会话分享表 - 支持会话分享功能"""
    __tablename__ = "conversation_shares"
    
    # 主键
    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()), comment="分享ID")
    thread_id = Column(String(64), ForeignKey('conversation_threads.id'), nullable=False, comment="线程ID")
    
    # 分享信息
    share_token = Column(String(64), unique=True, nullable=False, comment="分享令牌")
    share_type = Column(String(20), default="read_only", comment="分享类型: read_only, collaborative")
    
    # 分享设置
    is_public = Column(Boolean, default=False, comment="是否公开")
    password_protected = Column(Boolean, default=False, comment="是否密码保护")
    password_hash = Column(String(255), nullable=True, comment="密码哈希")
    
    # 有效期
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    
    # 时间戳
    create_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    # 索引
    __table_args__ = (
        Index('idx_share_token', 'share_token'),
        Index('idx_thread_share', 'thread_id'),
    )
