# 数据库配置文件
# 支持多环境配置：development, test, production

environments:
  development:
    # 服务器主数据库（用户、对话、知识库元数据等）
    server_db:
      type: postgresql
      host: localhost
      port: 5432
      database: xm
      username: postgres
      password: "fa6Z363@3bc6af5134"
      pool_size: 10
      max_overflow: 20
      echo: false
      connect_timeout: 30
      
    # LightRAG知识库数据库
    lightrag_db:
      type: postgresql
      host: localhost
      port: 5432
      database: lightrag
      username: postgres
      password: "fa6Z363@3bc6af5134"
      pool_size: 50
      max_overflow: 100
      echo: false
      connect_timeout: 30
      
    # Neo4j图数据库
    neo4j:
      uri: bolt://localhost:7687
      username: neo4j
      password: "A620250234Neo4j"
      encrypted: false
      trust: TRUST_ALL_CERTIFICATES
      connection_timeout: 30
      max_connection_lifetime: 3600
      max_connection_pool_size: 100
      connection_acquisition_timeout: 60
      
    # Milvus向量数据库
    milvus:
      uri: http://localhost:19530
      user: "root"
      password: "A6pgsql20200624"
      db_name: "default"
      timeout: 30
      
    # MinIO对象存储
    minio:
      uri: http://localhost:9000
      access_key: minioadmin
      secret_key: A6pgsql20200624
      secure: false
      region: us-east-1
      bucket_name: yuxi-know 
      
    # Redis缓存数据库
    redis:
      host: localhost
      port: 6379
      password: "A6pgsql202#00624"
      db: 0
      max_connections: 20
      connection_timeout: 30
      socket_timeout: 30
      retry_on_timeout: true
      decode_responses: true
      
  test:
    # 测试环境配置
    server_db:
      type: postgresql
      host: localhost
      port: 5432
      database: yuxi_test
      username: yuxi_test_user
      password: yuxi_test_password
      pool_size: 5
      max_overflow: 10
      echo: false
      connect_timeout: 30
      
    lightrag_db:
      type: postgresql
      host: localhost
      port: 5432
      database: lightrag_test
      username: lightrag_test_user
      password: lightrag_test_password
      pool_size: 20
      max_overflow: 40
      echo: false
      connect_timeout: 30
      
    neo4j:
      uri: bolt://localhost:7687
      username: neo4j
      password: neo4j_test_password
      encrypted: false
      trust: TRUST_ALL_CERTIFICATES
      connection_timeout: 30
      max_connection_lifetime: 3600
      max_connection_pool_size: 50
      connection_acquisition_timeout: 60
      
    milvus:
      uri: http://localhost:19530
      user: ""
      password: ""
      db_name: "test"
      timeout: 30
      
    minio:
      uri: http://localhost:9000
      access_key: minioadmin
      secret_key: A6pgsql20200624
      secure: false
      region: us-east-1
      bucket_name: yuxi-know-test 
      
    redis:
      host: localhost
      port: 6379
      password: ""
      db: 1
      max_connections: 10
      connection_timeout: 30
      socket_timeout: 30
      retry_on_timeout: true
      decode_responses: true

  production:
    # 生产环境配置 - 使用环境变量
    server_db:
      type: postgresql
      host: ${POSTGRES_HOST:-postgres}
      port: ${POSTGRES_PORT:-5432}
      database: ${POSTGRES_DB:-yuxi_prod}
      username: ${POSTGRES_USER:-yuxi_user}
      password: ${POSTGRES_PASSWORD}
      pool_size: ${POSTGRES_POOL_SIZE:-20}
      max_overflow: ${POSTGRES_MAX_OVERFLOW:-50}
      echo: false
      connect_timeout: 30
      
    lightrag_db:
      type: postgresql
      host: ${LIGHTRAG_POSTGRES_HOST:-postgres-lightrag}
      port: ${LIGHTRAG_POSTGRES_PORT:-5432}
      database: ${LIGHTRAG_POSTGRES_DB:-lightrag}
      username: ${LIGHTRAG_POSTGRES_USER:-lightrag}
      password: ${LIGHTRAG_POSTGRES_PASSWORD}
      pool_size: ${LIGHTRAG_POSTGRES_POOL_SIZE:-50}
      max_overflow: ${LIGHTRAG_POSTGRES_MAX_OVERFLOW:-100}
      echo: false
      connect_timeout: 30
      
    neo4j:
      uri: ${NEO4J_URI:-bolt://graph:7687}
      username: ${NEO4J_USERNAME:-neo4j}
      password: ${NEO4J_PASSWORD}
      encrypted: ${NEO4J_ENCRYPTED:-true}
      trust: ${NEO4J_TRUST:-TRUST_SYSTEM_CA_SIGNED_CERTIFICATES}
      connection_timeout: 30
      max_connection_lifetime: 3600
      max_connection_pool_size: ${NEO4J_POOL_SIZE:-100}
      connection_acquisition_timeout: 60
      
    milvus:
      uri: ${MILVUS_URI:-http://milvus:19530}
      user: ${MILVUS_USER:-}
      password: ${MILVUS_PASSWORD:-}
      db_name: ${MILVUS_DB:-default}
      timeout: 30
      
    minio:
      uri: ${MINIO_URI:-http://milvus-minio:9000}
      access_key: ${MINIO_ACCESS_KEY:-minioadmin}
      secret_key: ${MINIO_SECRET_KEY:-minioadmin}
      secure: ${MINIO_SECURE:-false}
      region: ${MINIO_REGION:-us-east-1}
      
    redis:
      host: ${REDIS_HOST:-redis}
      port: ${REDIS_PORT:-6379}
      password: ${REDIS_PASSWORD:-}
      db: ${REDIS_DB:-0}
      max_connections: ${REDIS_MAX_CONNECTIONS:-50}
      connection_timeout: ${REDIS_CONNECTION_TIMEOUT:-30}
      socket_timeout: ${REDIS_SOCKET_TIMEOUT:-30}
      retry_on_timeout: ${REDIS_RETRY_ON_TIMEOUT:-true}
      decode_responses: ${REDIS_DECODE_RESPONSES:-true}

# 默认环境配置
default_environment: development

# 连接重试配置
retry_config:
  max_retries: 3
  retry_delay: 1  # 秒
  backoff_factor: 2

# 健康检查配置
health_check:
  enabled: true
  interval: 60  # 秒
  timeout: 10   # 秒
  
# 日志配置
logging:
  level: INFO
  log_queries: false
  log_slow_queries: true
  slow_query_threshold: 1.0  # 秒