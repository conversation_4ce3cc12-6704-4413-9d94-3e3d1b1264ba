"""
PostgreSQL 检查点存储器

基于 PostgreSQL 的 LangGraph 检查点存储器实现，替换 AsyncSqliteSaver
"""

import json
import uuid
from typing import Any, Dict, List, Optional, Tuple, Iterator
from datetime import datetime

from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata
from langgraph.checkpoint.base import CheckpointTuple
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils import logger
from server.models.conversation_models import ConversationCheckpoint, ConversationThread, ConversationMessage


class AsyncPostgreSQLSaver(BaseCheckpointSaver):
    """
    基于 PostgreSQL 的异步检查点存储器
    
    实现 LangGraph 的检查点接口，将状态数据存储到 PostgreSQL 数据库中
    """
    
    def __init__(self, db_session_factory):
        """
        初始化 PostgreSQL 检查点存储器
        
        Args:
            db_session_factory: 数据库会话工厂函数
        """
        super().__init__()
        self.db_session_factory = db_session_factory
        logger.info("PostgreSQL 检查点存储器初始化完成")
    
    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        """
        获取检查点元组
        
        Args:
            config: 运行配置，包含 thread_id 等信息
            
        Returns:
            CheckpointTuple: 检查点元组或 None
        """
        try:
            thread_id = config["configurable"]["thread_id"]
            checkpoint_ns = config["configurable"].get("checkpoint_ns", "")
            
            async with self.db_session_factory() as session:
                # 查询最新的检查点
                query = text("""
                    SELECT checkpoint_id, checkpoint_data, meta_data, parent_checkpoint_id, create_at
                    FROM conversation_checkpoints
                    WHERE thread_id = :thread_id AND checkpoint_ns = :checkpoint_ns
                    ORDER BY create_at DESC
                    LIMIT 1
                """)
                
                result = await session.execute(query, {
                    "thread_id": thread_id,
                    "checkpoint_ns": checkpoint_ns
                })
                row = result.fetchone()
                
                if not row:
                    return None
                
                # 构建检查点对象
                checkpoint = Checkpoint(
                    v=1,
                    ts=row.create_at.isoformat(),
                    id=row.checkpoint_id,
                    channel_values=row.checkpoint_data.get("channel_values", {}),
                    channel_versions=row.checkpoint_data.get("channel_versions", {}),
                    versions_seen=row.checkpoint_data.get("versions_seen", {}),
                    pending_sends=row.checkpoint_data.get("pending_sends", [])
                )
                
                metadata = CheckpointMetadata(
                    source="update",
                    step=row.meta_data.get("step", -1) if row.meta_data else -1,
                    writes=row.meta_data.get("writes", {}) if row.meta_data else {},
                    parents=row.meta_data.get("parents", {}) if row.meta_data else {}
                )
                
                parent_config = None
                if row.parent_checkpoint_id:
                    parent_config = {
                        "configurable": {
                            **config["configurable"],
                            "checkpoint_id": row.parent_checkpoint_id
                        }
                    }
                
                return CheckpointTuple(
                    config=config,
                    checkpoint=checkpoint,
                    metadata=metadata,
                    parent_config=parent_config
                )
                
        except Exception as e:
            logger.error(f"获取检查点失败: {e}")
            return None
    
    async def alist(
        self, 
        config: RunnableConfig, 
        *, 
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[RunnableConfig] = None,
        limit: Optional[int] = None
    ) -> List[CheckpointTuple]:
        """
        列出检查点
        
        Args:
            config: 运行配置
            filter: 过滤条件
            before: 在此检查点之前
            limit: 限制数量
            
        Returns:
            List[CheckpointTuple]: 检查点列表
        """
        try:
            thread_id = config["configurable"]["thread_id"]
            checkpoint_ns = config["configurable"].get("checkpoint_ns", "")
            
            async with self.db_session_factory() as session:
                query_parts = [
                    "SELECT checkpoint_id, checkpoint_data, meta_data, parent_checkpoint_id, create_at",
                    "FROM conversation_checkpoints",
                    "WHERE thread_id = :thread_id AND checkpoint_ns = :checkpoint_ns"
                ]
                
                params = {
                    "thread_id": thread_id,
                    "checkpoint_ns": checkpoint_ns
                }
                
                # 添加 before 条件
                if before:
                    before_checkpoint_id = before["configurable"].get("checkpoint_id")
                    if before_checkpoint_id:
                        query_parts.append("AND create_at < (SELECT create_at FROM conversation_checkpoints WHERE checkpoint_id = :before_checkpoint_id)")
                        params["before_checkpoint_id"] = before_checkpoint_id
                
                query_parts.append("ORDER BY create_at DESC")
                
                # 添加限制
                if limit:
                    query_parts.append(f"LIMIT {limit}")
                
                query = text(" ".join(query_parts))
                result = await session.execute(query, params)
                rows = result.fetchall()
                
                checkpoints = []
                for row in rows:
                    checkpoint = Checkpoint(
                        v=1,
                        ts=row.create_at.isoformat(),
                        id=row.checkpoint_id,
                        channel_values=row.checkpoint_data.get("channel_values", {}),
                        channel_versions=row.checkpoint_data.get("channel_versions", {}),
                        versions_seen=row.checkpoint_data.get("versions_seen", {}),
                        pending_sends=row.checkpoint_data.get("pending_sends", [])
                    )
                    
                    metadata = CheckpointMetadata(
                        source="update",
                        step=row.meta_data.get("step", -1) if row.meta_data else -1,
                        writes=row.meta_data.get("writes", {}) if row.meta_data else {},
                        parents=row.meta_data.get("parents", {}) if row.meta_data else {}
                    )
                    
                    parent_config = None
                    if row.parent_checkpoint_id:
                        parent_config = {
                            "configurable": {
                                **config["configurable"],
                                "checkpoint_id": row.parent_checkpoint_id
                            }
                        }
                    
                    checkpoint_config = {
                        "configurable": {
                            **config["configurable"],
                            "checkpoint_id": row.checkpoint_id
                        }
                    }
                    
                    checkpoints.append(CheckpointTuple(
                        config=checkpoint_config,
                        checkpoint=checkpoint,
                        metadata=metadata,
                        parent_config=parent_config
                    ))
                
                return checkpoints
                
        except Exception as e:
            logger.error(f"列出检查点失败: {e}")
            return []
    
    async def aput(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Any]
    ) -> RunnableConfig:
        """
        保存检查点
        
        Args:
            config: 运行配置
            checkpoint: 检查点对象
            metadata: 检查点元数据
            new_versions: 新版本信息
            
        Returns:
            RunnableConfig: 更新后的配置
        """
        try:
            thread_id = config["configurable"]["thread_id"]
            checkpoint_ns = config["configurable"].get("checkpoint_ns", "")
            checkpoint_id = str(uuid.uuid4())
            
            # 准备检查点数据
            checkpoint_data = {
                "channel_values": checkpoint.channel_values,
                "channel_versions": checkpoint.channel_versions,
                "versions_seen": checkpoint.versions_seen,
                "pending_sends": checkpoint.pending_sends
            }
            
            # 准备元数据
            metadata_dict = {
                "step": metadata.step,
                "writes": metadata.writes,
                "parents": metadata.parents
            }
            
            async with self.db_session_factory() as session:
                # 确保线程存在
                await self._ensure_thread_exists(session, thread_id, config)
                
                # 插入检查点
                insert_query = text("""
                    INSERT INTO conversation_checkpoints
                    (checkpoint_id, thread_id, checkpoint_ns, checkpoint_data, meta_data, create_at)
                    VALUES (:checkpoint_id, :thread_id, :checkpoint_ns, :checkpoint_data, :meta_data, :create_at)
                """)
                
                await session.execute(insert_query, {
                    "checkpoint_id": checkpoint_id,
                    "thread_id": thread_id,
                    "checkpoint_ns": checkpoint_ns,
                    "checkpoint_data": checkpoint_data,
                    "meta_data": metadata_dict,
                    "create_at": datetime.now()
                })
                
                await session.commit()
                
                # 返回更新后的配置
                return {
                    "configurable": {
                        **config["configurable"],
                        "checkpoint_id": checkpoint_id
                    }
                }
                
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")
            raise
    
    async def _ensure_thread_exists(self, session: AsyncSession, thread_id: str, config: RunnableConfig):
        """
        确保会话线程存在
        
        Args:
            session: 数据库会话
            thread_id: 线程ID
            config: 运行配置
        """
        try:
            # 检查线程是否存在
            check_query = text("SELECT id FROM conversation_threads WHERE id = :thread_id")
            result = await session.execute(check_query, {"thread_id": thread_id})
            
            if not result.fetchone():
                # 创建新线程
                user_id = config["configurable"].get("user_id", "unknown")
                agent_id = config["configurable"].get("agent_id", "chatbot")
                
                insert_query = text("""
                    INSERT INTO conversation_threads 
                    (id, user_id, agent_id, title, status, create_at, update_at)
                    VALUES (:id, :user_id, :agent_id, :title, :status, :create_at, :update_at)
                """)
                
                await session.execute(insert_query, {
                    "id": thread_id,
                    "user_id": user_id,
                    "agent_id": agent_id,
                    "title": f"会话 {thread_id[:8]}",
                    "status": "active",
                    "create_at": datetime.now(),
                    "update_at": datetime.now()
                })
                
                logger.debug(f"创建新会话线程: {thread_id}")
                
        except Exception as e:
            logger.error(f"确保线程存在失败: {e}")
            raise
    
    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        """同步版本的 get_tuple (不推荐使用)"""
        raise NotImplementedError("请使用异步版本 aget_tuple")
    
    def list(self, config: RunnableConfig, *, filter: Optional[Dict[str, Any]] = None, before: Optional[RunnableConfig] = None, limit: Optional[int] = None) -> Iterator[CheckpointTuple]:
        """同步版本的 list (不推荐使用)"""
        raise NotImplementedError("请使用异步版本 alist")
    
    def put(self, config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata, new_versions: Dict[str, Any]) -> RunnableConfig:
        """同步版本的 put (不推荐使用)"""
        raise NotImplementedError("请使用异步版本 aput")
