"""
企业会话管理 API 路由

提供完整的会话管理 REST API 接口
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from pydantic import BaseModel, Field
from datetime import datetime

from server.dependencies import get_required_user, get_database_manager_dependency
from server.models.user_model import User
from src.database.manager import UnifiedDatabaseManager
from src.services.conversation_service import ConversationService
from src.services.conversation_cache import ConversationCacheService
from src.utils import logger

# 创建路由器
conversation = APIRouter(prefix="/api/conversations", tags=["会话管理"])


# ==================== 请求/响应模型 ====================

class ConversationCreateRequest(BaseModel):
    """创建会话请求"""
    agent_id: str = Field(..., description="智能体ID")
    title: Optional[str] = Field(None, description="会话标题")
    description: Optional[str] = Field(None, description="会话描述")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class ConversationUpdateRequest(BaseModel):
    """更新会话请求"""
    title: Optional[str] = Field(None, description="会话标题")
    description: Optional[str] = Field(None, description="会话描述")
    status: Optional[str] = Field(None, description="会话状态")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class ConversationResponse(BaseModel):
    """会话响应"""
    id: str
    user_id: str
    agent_id: str
    title: Optional[str]
    description: Optional[str]
    status: str
    create_at: str
    update_at: str
    last_message_at: Optional[str]
    message_count: int
    meta_data: Dict[str, Any]


class ConversationListResponse(BaseModel):
    """会话列表响应"""
    conversations: List[ConversationResponse]
    total: int
    page: int
    page_size: int
    has_more: bool


class MessageResponse(BaseModel):
    """消息响应"""
    id: str
    role: str
    content: str
    content_type: str
    message_index: int
    meta_data: Dict[str, Any]
    create_at: str


class ConversationStatsResponse(BaseModel):
    """会话统计响应"""
    total_conversations: int
    active_conversations: int
    archived_conversations: int
    total_messages: int
    agents_used: int
    first_conversation_at: Optional[str]
    last_activity_at: Optional[str]
    by_agent: List[Dict[str, Any]]


# ==================== 依赖注入 ====================

async def get_conversation_service(
    db_manager: UnifiedDatabaseManager = Depends(get_database_manager_dependency)
) -> ConversationService:
    """获取会话服务实例"""
    try:
        # 获取 PostgreSQL 适配器
        pg_adapter = await db_manager.get_postgresql_adapter('server_db')
        if not pg_adapter:
            raise HTTPException(status_code=500, detail="数据库服务不可用")
        
        # 创建数据库会话工厂
        async def session_factory():
            return pg_adapter.get_session_context()
        
        # 获取 Redis 缓存服务
        cache_service = None
        try:
            redis_adapter = await db_manager.get_redis_adapter()
            if redis_adapter:
                cache_service = ConversationCacheService(redis_adapter.client)
        except Exception as e:
            logger.warning(f"Redis 缓存服务不可用: {e}")
        
        return ConversationService(session_factory, cache_service)
        
    except Exception as e:
        logger.error(f"创建会话服务失败: {e}")
        raise HTTPException(status_code=500, detail="会话服务初始化失败")


# ==================== 会话 CRUD API ====================

@conversation.post("/", response_model=ConversationResponse)
async def create_conversation(
    request: ConversationCreateRequest,
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """创建新会话"""
    try:
        conversation_obj = await service.create_conversation(
            user_id=current_user.id,
            agent_id=request.agent_id,
            title=request.title,
            description=request.description,
            meta_data=request.meta_data
        )
        
        return ConversationResponse(
            id=conversation_obj.id,
            user_id=conversation_obj.user_id,
            agent_id=conversation_obj.agent_id,
            title=conversation_obj.title,
            description=conversation_obj.description,
            status=conversation_obj.status,
            create_at=conversation_obj.create_at.isoformat(),
            update_at=conversation_obj.update_at.isoformat(),
            last_message_at=conversation_obj.last_message_at.isoformat() if conversation_obj.last_message_at else None,
            message_count=conversation_obj.message_count or 0,
            meta_data=conversation_obj.meta_data or {}
        )
        
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@conversation.get("/", response_model=ConversationListResponse)
async def get_user_conversations(
    agent_id: Optional[str] = Query(None, description="智能体ID过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_by: str = Query("update_at", description="排序字段"),
    order_desc: bool = Query(True, description="是否降序"),
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """获取用户会话列表"""
    try:
        offset = (page - 1) * page_size
        
        conversations, total = await service.get_user_conversations(
            user_id=current_user.id,
            agent_id=agent_id,
            status=status,
            limit=page_size,
            offset=offset,
            order_by=order_by,
            order_desc=order_desc
        )
        
        # 转换为响应模型
        conversation_responses = [
            ConversationResponse(**conv) for conv in conversations
        ]
        
        has_more = offset + page_size < total
        
        return ConversationListResponse(
            conversations=conversation_responses,
            total=total,
            page=page,
            page_size=page_size,
            has_more=has_more
        )
        
    except Exception as e:
        logger.error(f"获取用户会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")


@conversation.get("/{thread_id}", response_model=ConversationResponse)
async def get_conversation(
    thread_id: str,
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """获取会话详情"""
    try:
        conversation_obj = await service.get_conversation(thread_id, current_user.id)
        
        if not conversation_obj:
            raise HTTPException(status_code=404, detail="会话不存在或无权限访问")
        
        return ConversationResponse(
            id=conversation_obj.id,
            user_id=conversation_obj.user_id,
            agent_id=conversation_obj.agent_id,
            title=conversation_obj.title,
            description=conversation_obj.description,
            status=conversation_obj.status,
            create_at=conversation_obj.create_at.isoformat(),
            update_at=conversation_obj.update_at.isoformat(),
            last_message_at=conversation_obj.last_message_at.isoformat() if conversation_obj.last_message_at else None,
            message_count=conversation_obj.message_count or 0,
            meta_data=conversation_obj.meta_data or {}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话详情失败: {str(e)}")


@conversation.put("/{thread_id}", response_model=Dict[str, Any])
async def update_conversation(
    thread_id: str,
    request: ConversationUpdateRequest,
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """更新会话信息"""
    try:
        success = await service.update_conversation(
            thread_id=thread_id,
            user_id=current_user.id,
            title=request.title,
            description=request.description,
            status=request.status,
            meta_data=request.meta_data
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或无权限修改")
        
        return {"success": True, "message": "会话更新成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新会话失败: {str(e)}")


@conversation.delete("/{thread_id}")
async def delete_conversation(
    thread_id: str,
    soft_delete: bool = Query(True, description="是否软删除"),
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """删除会话"""
    try:
        success = await service.delete_conversation(
            thread_id=thread_id,
            user_id=current_user.id,
            soft_delete=soft_delete
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或无权限删除")
        
        return {"success": True, "message": "会话删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}")


# ==================== 会话消息 API ====================

@conversation.get("/{thread_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    thread_id: str,
    limit: int = Query(100, ge=1, le=500, description="消息数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    role_filter: Optional[str] = Query(None, description="角色过滤"),
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """获取会话消息列表"""
    try:
        messages = await service.get_conversation_messages(
            thread_id=thread_id,
            user_id=current_user.id,
            limit=limit,
            offset=offset,
            role_filter=role_filter
        )
        
        return [MessageResponse(**msg) for msg in messages]
        
    except Exception as e:
        logger.error(f"获取会话消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")


# ==================== 会话搜索和统计 API ====================

@conversation.get("/search", response_model=List[ConversationResponse])
async def search_conversations(
    q: str = Query(..., description="搜索关键词"),
    agent_id: Optional[str] = Query(None, description="智能体ID过滤"),
    limit: int = Query(20, ge=1, le=100, description="结果数量限制"),
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """搜索用户会话"""
    try:
        conversations = await service.search_conversations(
            user_id=current_user.id,
            query=q,
            agent_id=agent_id,
            limit=limit
        )
        
        return [ConversationResponse(**conv) for conv in conversations]
        
    except Exception as e:
        logger.error(f"搜索会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索会话失败: {str(e)}")


@conversation.get("/stats", response_model=ConversationStatsResponse)
async def get_conversation_stats(
    current_user: User = Depends(get_required_user),
    service: ConversationService = Depends(get_conversation_service)
):
    """获取用户会话统计信息"""
    try:
        stats = await service.get_user_conversation_stats(current_user.id)
        return ConversationStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"获取会话统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话统计失败: {str(e)}")
