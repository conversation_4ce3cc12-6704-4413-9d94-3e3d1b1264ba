-- 企业会话管理数据库迁移脚本
-- 创建会话相关表结构

-- 1. 会话线程表 (扩展版本)
CREATE TABLE IF NOT EXISTS conversation_threads (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL,
    agent_id VARCHAR(64) NOT NULL,
    
    -- 会话元数据
    title VARCHAR(255),
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 时间戳
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_message_at TIMESTAMP,
    
    -- 扩展信息
    meta_data JSONB,
    tags JSONB,
    
    -- 统计信息
    message_count INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_agent_status 
ON conversation_threads(user_id, agent_id, status);

CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_update_time 
ON conversation_threads(user_id, update_at);

CREATE INDEX IF NOT EXISTS idx_conversation_threads_agent_create_time 
ON conversation_threads(agent_id, create_at);

-- 2. 会话消息表
CREATE TABLE IF NOT EXISTS conversation_messages (
    id VARCHAR(64) PRIMARY KEY,
    thread_id VARCHAR(64) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- 消息内容
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'text',
    
    -- 消息元数据
    message_index INTEGER NOT NULL,
    parent_message_id VARCHAR(64),
    
    -- 扩展信息
    meta_data JSONB,
    tool_calls JSONB,
    attachments JSONB,
    
    -- 时间戳
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_index 
ON conversation_messages(thread_id, message_index);

CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_role_time 
ON conversation_messages(thread_id, role, create_at);

CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_create_time 
ON conversation_messages(thread_id, create_at);

-- 3. 会话检查点表 (LangGraph 状态存储)
CREATE TABLE IF NOT EXISTS conversation_checkpoints (
    checkpoint_id VARCHAR(64) PRIMARY KEY,
    thread_id VARCHAR(64) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- 检查点信息
    checkpoint_ns VARCHAR(100) DEFAULT '',
    parent_checkpoint_id VARCHAR(64),
    
    -- 状态数据
    checkpoint_data JSONB NOT NULL,
    meta_data JSONB,
    
    -- 时间戳
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conversation_checkpoints_thread_checkpoint 
ON conversation_checkpoints(thread_id, checkpoint_ns, create_at);

CREATE INDEX IF NOT EXISTS idx_conversation_checkpoints_parent 
ON conversation_checkpoints(parent_checkpoint_id);

-- 4. 会话摘要表 (用于长对话的摘要存储)
CREATE TABLE IF NOT EXISTS conversation_summaries (
    id VARCHAR(64) PRIMARY KEY,
    thread_id VARCHAR(64) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- 摘要信息
    summary_type VARCHAR(50) DEFAULT 'auto',
    summary_content TEXT NOT NULL,
    
    -- 摘要范围
    start_message_index INTEGER NOT NULL,
    end_message_index INTEGER NOT NULL,
    
    -- 时间戳
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_thread_range 
ON conversation_summaries(thread_id, start_message_index, end_message_index);

-- 5. 用户会话偏好设置表
CREATE TABLE IF NOT EXISTS user_conversation_preferences (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL,
    agent_id VARCHAR(64), -- 为空表示全局设置
    
    -- 偏好设置
    preferences JSONB NOT NULL,
    
    -- 时间戳
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_conversation_preferences_user_agent 
ON user_conversation_preferences(user_id, agent_id);

-- 6. 会话分享表 (支持会话分享功能)
CREATE TABLE IF NOT EXISTS conversation_shares (
    id VARCHAR(64) PRIMARY KEY,
    thread_id VARCHAR(64) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- 分享信息
    share_token VARCHAR(64) UNIQUE NOT NULL,
    share_type VARCHAR(20) DEFAULT 'read_only',
    
    -- 分享设置
    is_public BOOLEAN DEFAULT FALSE,
    password_protected BOOLEAN DEFAULT FALSE,
    password_hash VARCHAR(255),
    
    -- 有效期
    expires_at TIMESTAMP,
    
    -- 时间戳
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conversation_shares_token 
ON conversation_shares(share_token);

CREATE INDEX IF NOT EXISTS idx_conversation_shares_thread 
ON conversation_shares(thread_id);

-- 7. 创建触发器来自动更新 update_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 conversation_threads 表创建触发器
DROP TRIGGER IF EXISTS update_conversation_threads_updated_at ON conversation_threads;
CREATE TRIGGER update_conversation_threads_updated_at
    BEFORE UPDATE ON conversation_threads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为 user_conversation_preferences 表创建触发器
DROP TRIGGER IF EXISTS update_user_conversation_preferences_updated_at ON user_conversation_preferences;
CREATE TRIGGER update_user_conversation_preferences_updated_at
    BEFORE UPDATE ON user_conversation_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 8. 插入一些示例数据 (可选)
-- 注意：这里只是示例，实际使用时可能需要根据具体情况调整

-- 示例：创建一个测试会话
-- INSERT INTO conversation_threads (id, user_id, agent_id, title, status, meta_data)
-- VALUES ('test-thread-001', 'test-user-001', 'chatbot', '测试会话', 'active', '{"test": true}');

-- 示例：创建一个测试消息
-- INSERT INTO conversation_messages (id, thread_id, role, content, message_index, meta_data)
-- VALUES ('test-msg-001', 'test-thread-001', 'human', '你好', 0, '{"test": true}');

-- 9. 权限设置 (根据需要调整)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON conversation_threads TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON conversation_messages TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON conversation_checkpoints TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON conversation_summaries TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON user_conversation_preferences TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON conversation_shares TO your_app_user;

-- 10. 查看表结构 (验证脚本)
-- \d conversation_threads
-- \d conversation_messages
-- \d conversation_checkpoints
-- \d conversation_summaries
-- \d user_conversation_preferences
-- \d conversation_shares

COMMENT ON TABLE conversation_threads IS '会话线程表 - 存储会话基本信息';
COMMENT ON TABLE conversation_messages IS '会话消息表 - 存储对话消息';
COMMENT ON TABLE conversation_checkpoints IS '会话检查点表 - LangGraph状态存储';
COMMENT ON TABLE conversation_summaries IS '会话摘要表 - 长对话摘要';
COMMENT ON TABLE user_conversation_preferences IS '用户会话偏好表 - 用户个性化设置';
COMMENT ON TABLE conversation_shares IS '会话分享表 - 会话分享功能';

-- 完成
SELECT 'Enterprise conversation management tables created successfully!' AS result;
