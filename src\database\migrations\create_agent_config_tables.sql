-- 创建智能体配置相关表
-- 执行时间: 2025-07-17

-- 智能体系统配置表
CREATE TABLE IF NOT EXISTS agent_system_configs (
    id SERIAL PRIMARY KEY,
    agent_name VARCHAR(100) NOT NULL,
    config_data JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_name)
);

-- 用户智能体配置表
CREATE TABLE IF NOT EXISTS user_agent_configs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    agent_name VARCHAR(100) NOT NULL,
    config_data JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, agent_name)
);

-- 知识库智能体配置表
CREATE TABLE IF NOT EXISTS kb_agent_configs (
    id SERIAL PRIMARY KEY,
    kb_id VARCHAR(100) NOT NULL,
    agent_name VARCHAR(100) NOT NULL,
    config_data JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(kb_id, agent_name)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_agent_system_configs_agent_name ON agent_system_configs(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_system_configs_is_active ON agent_system_configs(is_active);

CREATE INDEX IF NOT EXISTS idx_user_agent_configs_user_id ON user_agent_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_agent_configs_agent_name ON user_agent_configs(agent_name);
CREATE INDEX IF NOT EXISTS idx_user_agent_configs_is_active ON user_agent_configs(is_active);

CREATE INDEX IF NOT EXISTS idx_kb_agent_configs_kb_id ON kb_agent_configs(kb_id);
CREATE INDEX IF NOT EXISTS idx_kb_agent_configs_agent_name ON kb_agent_configs(agent_name);
CREATE INDEX IF NOT EXISTS idx_kb_agent_configs_is_active ON kb_agent_configs(is_active);

-- 插入默认配置数据
INSERT INTO agent_system_configs (agent_name, config_data) VALUES
    ('chatbot', '{"model": "openai/gpt-4", "temperature": 0.7, "max_tokens": 2000, "system_prompt": "你是一个有用的AI助手。", "tools": []}'),
    ('react', '{"model": "openai/gpt-4", "temperature": 0.3, "max_tokens": 2000, "system_prompt": "你是一个能够使用工具解决问题的AI助手。", "tools": ["calculator", "web_search"]}')
ON CONFLICT (agent_name) DO NOTHING;

-- 添加注释
COMMENT ON TABLE agent_system_configs IS '智能体系统级配置表';
COMMENT ON TABLE user_agent_configs IS '用户级智能体配置表';
COMMENT ON TABLE kb_agent_configs IS '知识库级智能体配置表';