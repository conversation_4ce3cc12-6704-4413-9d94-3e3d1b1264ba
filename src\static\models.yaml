####################################################
#
#  不要直接修改这个里面的文件，可能会有被覆盖的风险，
#  建议 复制一份 在 models.private.yml 中修改，
#  会自动加载
#
#####################################################

MODEL_NAMES:
  openai:
    name: OpenAI
    url: https://platform.openai.com/docs/models
    base_url: https://api.openai.com/v1
    default: gpt-3.5-turbo
    env:
      - OPENAI_API_KEY
    models:
      - gpt-4
      - gpt-4o
      - gpt-4o-mini
      - gpt-3.5-turbo

  deepseek:
    name: DeepSeek
    url: https://platform.deepseek.com/api-docs/zh-cn/pricing
    base_url: https://api.deepseek.com/v1
    default: deepseek-chat
    env:
      - DEEPSEEK_API_KEY
    models:
      - deepseek-chat
      - deepseek-reasoner

  zhipu:
    name: 智谱AI (Zhipu)
    url: https://open.bigmodel.cn/dev/api
    base_url: https://open.bigmodel.cn/api/paas/v4/
    default: glm-4-flash
    env:
      - ZHIPUAI_API_KEY
    models:
      - glm-4
      - glm-4-plus
      - glm-4-air
      - glm-4-flash
      - glm-z1-air

  siliconflow:
    name: SiliconFlow
    url: https://cloud.siliconflow.cn/models
    base_url: https://api.siliconflow.cn/v1
    default: Qwen/Qwen3-8B
    env:
      - SILICONFLOW_API_KEY
    models:
      - Pro/deepseek-ai/DeepSeek-R1
      - Pro/deepseek-ai/DeepSeek-V3
      - Qwen/QwQ-32B
      - Qwen/Qwen3-8B

  together.ai:
    name: Together.ai
    url: https://api.together.ai/models
    base_url: https://api.together.xyz/v1/
    default: meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
    env:
      - TOGETHER_API_KEY
    models:
      - meta-llama/Llama-3.3-70B-Instruct-Turbo
      - meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
      - deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free
      - Qwen/QwQ-32B

  dashscope:
    name: 阿里百炼 (DashScope)
    url: https://bailian.console.aliyun.com/?switchAgent=10226727&productCode=p_efm#/model-market
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    default: qwen3-235b-a22b
    env:
      - DASHSCOPE_API_KEY
    models:
      - qwen-max-latest
      - qwen3-235b-a22b
      - qwen3-32b

  ark:
    name: 豆包（Ark）
    url: https://console.volcengine.com/ark/region:ark+cn-beijing/model
    base_url: https://ark.cn-beijing.volces.com/api/v3
    default: doubao-1-5-pro-32k-250115
    env:
      - ARK_API_KEY
    models:
      - doubao-1-5-pro-32k-250115
      - doubao-1-5-lite-32k-250115
      - deepseek-r1-250120

  openrouter:
    name: OpenRouter
    url: https://openrouter.ai/models
    base_url: https://openrouter.ai/api/v1
    default: openai/gpt-4o
    env:
      - OPENROUTER_API_KEY
    models:
      - openai/gpt-4o
      - openai/gpt-4o-mini
      - google/gemini-2.5-pro-exp-03-25:free
      - x-ai/grok-3-beta
      - meta-llama/llama-4-maverick
      - meta-llama/llama-4-maverick:free
      - anthropic/claude-3.7-sonnet
      - anthropic/claude-3.7-sonnet:thinking


EMBED_MODEL_INFO:
  siliconflow/BAAI/bge-m3:
    name: BAAI/bge-m3
    dimension: 1024
    base_url: https://api.siliconflow.cn/v1/embeddings
    api_key: SILICONFLOW_API_KEY

  vllm/Qwen/Qwen3-Embedding-0.6B:
    name: Qwen3-Embedding-0.6B
    dimension: 1024
    base_url: http://localhost:8081/v1/embeddings
    api_key: no_api_key

  ollama/nomic-embed-text:
    name: nomic-embed-text
    base_url: http://localhost:11434/api/embed
    dimension: 768

  ollama/bge-m3:
    name: bge-m3
    base_url: http://localhost:11434/api/embed
    dimension: 1024

RERANKER_LIST:

  siliconflow/BAAI/bge-reranker-v2-m3:
    name: BAAI/bge-reranker-v2-m3
    base_url: https://api.siliconflow.cn/v1/rerank
    api_key: SILICONFLOW_API_KEY

  vllm/Qwen/Qwen3-Reranker-0.6B:
    name: Qwen/Qwen3-Reranker-0.6B
    base_url: http://localhost:8081/v1/rerank
    api_key: no_api_key
