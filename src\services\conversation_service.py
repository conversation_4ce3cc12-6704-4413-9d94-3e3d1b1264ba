"""
企业会话管理服务

提供完整的会话 CRUD 操作和用户会话列表功能
"""

import uuid
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import text, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils import logger
from src.services.conversation_cache import ConversationCacheService
from server.models.conversation_models import (
    ConversationThread, ConversationMessage, ConversationCheckpoint,
    ConversationSummary, UserConversationPreference
)


class ConversationService:
    """
    企业会话管理服务
    
    提供会话的完整生命周期管理，包括：
    - 会话 CRUD 操作
    - 用户会话列表管理
    - 会话消息管理
    - 会话搜索和过滤
    - 会话统计和分析
    """
    
    def __init__(self, db_session_factory, cache_service: Optional[ConversationCacheService] = None):
        """
        初始化会话管理服务
        
        Args:
            db_session_factory: 数据库会话工厂
            cache_service: 会话缓存服务 (可选)
        """
        self.db_session_factory = db_session_factory
        self.cache_service = cache_service
        logger.info("会话管理服务初始化完成")
    
    # ==================== 会话 CRUD 操作 ====================
    
    async def create_conversation(
        self,
        user_id: str,
        agent_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        meta_data: Optional[Dict] = None,
        thread_id: Optional[str] = None
    ) -> ConversationThread:
        """
        创建新会话
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            title: 会话标题
            description: 会话描述
            meta_data: 扩展元数据
            thread_id: 指定的线程ID (可选)
            
        Returns:
            ConversationThread: 创建的会话对象
        """
        try:
            if not thread_id:
                thread_id = str(uuid.uuid4())
            
            if not title:
                title = f"会话 {thread_id[:8]}"
            
            async with self.db_session_factory() as session:
                conversation = ConversationThread(
                    id=thread_id,
                    user_id=user_id,
                    agent_id=agent_id,
                    title=title,
                    description=description,
                    meta_data=meta_data or {},
                    status="active",
                    create_at=datetime.now(),
                    update_at=datetime.now()
                )
                
                session.add(conversation)
                await session.commit()
                await session.refresh(conversation)
                
                # 清除用户会话列表缓存
                if self.cache_service:
                    await self.cache_service.invalidate_user_sessions(user_id, agent_id)
                
                logger.info(f"创建新会话: {thread_id}, 用户: {user_id}, 智能体: {agent_id}")
                return conversation
                
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    async def get_conversation(self, thread_id: str, user_id: Optional[str] = None) -> Optional[ConversationThread]:
        """
        获取会话详情
        
        Args:
            thread_id: 线程ID
            user_id: 用户ID (用于权限验证)
            
        Returns:
            ConversationThread: 会话对象或 None
        """
        try:
            # 先尝试从缓存获取
            if self.cache_service:
                cached_meta = await self.cache_service.get_session_meta(thread_id)
                if cached_meta and (not user_id or cached_meta.get("user_id") == user_id):
                    # 从缓存数据构建对象 (简化版本)
                    logger.debug(f"从缓存获取会话元数据: {thread_id}")
            
            async with self.db_session_factory() as session:
                query = session.query(ConversationThread).filter(ConversationThread.id == thread_id)
                
                # 添加用户权限验证
                if user_id:
                    query = query.filter(ConversationThread.user_id == user_id)
                
                conversation = await session.execute(query)
                result = conversation.scalar_one_or_none()
                
                # 缓存会话元数据
                if result and self.cache_service:
                    meta_data = {
                        "id": result.id,
                        "user_id": result.user_id,
                        "agent_id": result.agent_id,
                        "title": result.title,
                        "status": result.status,
                        "create_at": result.create_at.isoformat(),
                        "update_at": result.update_at.isoformat(),
                        "message_count": result.message_count
                    }
                    await self.cache_service.set_session_meta(thread_id, meta_data)
                
                return result
                
        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            return None
    
    async def update_conversation(
        self,
        thread_id: str,
        user_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[str] = None,
        meta_data: Optional[Dict] = None
    ) -> bool:
        """
        更新会话信息
        
        Args:
            thread_id: 线程ID
            user_id: 用户ID
            title: 新标题
            description: 新描述
            status: 新状态
            meta_data: 新元数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            async with self.db_session_factory() as session:
                query = text("""
                    UPDATE conversation_threads
                    SET title = COALESCE(:title, title),
                        description = COALESCE(:description, description),
                        status = COALESCE(:status, status),
                        meta_data = COALESCE(:meta_data, meta_data),
                        update_at = :update_at
                    WHERE id = :thread_id AND user_id = :user_id
                """)
                
                result = await session.execute(query, {
                    "thread_id": thread_id,
                    "user_id": user_id,
                    "title": title,
                    "description": description,
                    "status": status,
                    "meta_data": meta_data,
                    "update_at": datetime.now()
                })
                
                await session.commit()
                
                if result.rowcount > 0:
                    # 清除相关缓存
                    if self.cache_service:
                        await self.cache_service.invalidate_user_sessions(user_id)
                        # 清除会话元数据缓存
                        cache_key = f"session_meta:{thread_id}"
                        await self.cache_service.redis.delete(cache_key)
                    
                    logger.info(f"更新会话成功: {thread_id}")
                    return True
                else:
                    logger.warning(f"会话不存在或无权限: {thread_id}")
                    return False
                
        except Exception as e:
            logger.error(f"更新会话失败: {e}")
            return False
    
    async def delete_conversation(self, thread_id: str, user_id: str, soft_delete: bool = True) -> bool:
        """
        删除会话
        
        Args:
            thread_id: 线程ID
            user_id: 用户ID
            soft_delete: 是否软删除 (默认 True)
            
        Returns:
            bool: 删除是否成功
        """
        try:
            async with self.db_session_factory() as session:
                if soft_delete:
                    # 软删除：更新状态为 deleted
                    query = text("""
                        UPDATE conversation_threads 
                        SET status = 'deleted', update_at = :update_at
                        WHERE id = :thread_id AND user_id = :user_id
                    """)
                    
                    result = await session.execute(query, {
                        "thread_id": thread_id,
                        "user_id": user_id,
                        "update_at": datetime.now()
                    })
                else:
                    # 硬删除：物理删除记录
                    query = text("""
                        DELETE FROM conversation_threads 
                        WHERE id = :thread_id AND user_id = :user_id
                    """)
                    
                    result = await session.execute(query, {
                        "thread_id": thread_id,
                        "user_id": user_id
                    })
                
                await session.commit()
                
                if result.rowcount > 0:
                    # 清除相关缓存
                    if self.cache_service:
                        await self.cache_service.invalidate_user_sessions(user_id)
                        await self.cache_service.clear_session_messages(thread_id)
                        # 清除会话相关的所有缓存
                        cache_patterns = [
                            f"session_meta:{thread_id}",
                            f"session_messages:{thread_id}",
                            f"session_checkpoint:{thread_id}*"
                        ]
                        for pattern in cache_patterns:
                            if "*" in pattern:
                                keys = await self.cache_service.redis.keys(pattern)
                                if keys:
                                    await self.cache_service.redis.delete(*keys)
                            else:
                                await self.cache_service.redis.delete(pattern)
                    
                    logger.info(f"删除会话成功: {thread_id}, 软删除: {soft_delete}")
                    return True
                else:
                    logger.warning(f"会话不存在或无权限: {thread_id}")
                    return False
                
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False
    
    # ==================== 用户会话列表管理 ====================
    
    async def get_user_conversations(
        self,
        user_id: str,
        agent_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        order_by: str = "update_at",
        order_desc: bool = True
    ) -> Tuple[List[Dict], int]:
        """
        获取用户会话列表
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID (可选)
            status: 会话状态 (可选)
            limit: 限制数量
            offset: 偏移量
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            Tuple[List[Dict], int]: (会话列表, 总数)
        """
        try:
            # 先尝试从缓存获取
            if self.cache_service and offset == 0 and limit <= 50:
                cached_sessions = await self.cache_service.get_user_sessions(user_id, agent_id)
                if cached_sessions:
                    # 应用状态过滤
                    if status:
                        cached_sessions = [s for s in cached_sessions if s.get("status") == status]
                    
                    # 应用分页
                    total = len(cached_sessions)
                    sessions = cached_sessions[offset:offset + limit]
                    
                    logger.debug(f"从缓存获取用户会话列表: {user_id}, 数量: {len(sessions)}")
                    return sessions, total
            
            async with self.db_session_factory() as session:
                # 构建查询条件
                conditions = [f"user_id = '{user_id}'"]
                
                if agent_id:
                    conditions.append(f"agent_id = '{agent_id}'")
                
                if status:
                    conditions.append(f"status = '{status}'")
                else:
                    conditions.append("status != 'deleted'")  # 默认不显示已删除的会话
                
                where_clause = " AND ".join(conditions)
                
                # 构建排序
                order_direction = "DESC" if order_desc else "ASC"
                order_clause = f"ORDER BY {order_by} {order_direction}"
                
                # 查询会话列表
                query = text(f"""
                    SELECT id, user_id, agent_id, title, description, status,
                           create_at, update_at, last_message_at, message_count, meta_data
                    FROM conversation_threads
                    WHERE {where_clause}
                    {order_clause}
                    LIMIT :limit OFFSET :offset
                """)
                
                result = await session.execute(query, {"limit": limit, "offset": offset})
                rows = result.fetchall()
                
                # 查询总数
                count_query = text(f"""
                    SELECT COUNT(*) FROM conversation_threads WHERE {where_clause}
                """)
                count_result = await session.execute(count_query)
                total = count_result.scalar()
                
                # 格式化结果
                sessions = []
                for row in rows:
                    session_data = {
                        "id": row.id,
                        "user_id": row.user_id,
                        "agent_id": row.agent_id,
                        "title": row.title,
                        "description": row.description,
                        "status": row.status,
                        "create_at": row.create_at.isoformat() if row.create_at else None,
                        "update_at": row.update_at.isoformat() if row.update_at else None,
                        "last_message_at": row.last_message_at.isoformat() if row.last_message_at else None,
                        "message_count": row.message_count or 0,
                        "meta_data": row.meta_data or {}
                    }
                    sessions.append(session_data)
                
                # 缓存结果 (仅缓存第一页的默认查询)
                if self.cache_service and offset == 0 and not status and limit <= 50:
                    await self.cache_service.set_user_sessions(user_id, sessions, agent_id)
                
                logger.debug(f"获取用户会话列表: {user_id}, 数量: {len(sessions)}, 总数: {total}")
                return sessions, total
                
        except Exception as e:
            logger.error(f"获取用户会话列表失败: {e}")
            return [], 0

    # ==================== 会话消息管理 ====================

    async def add_message(
        self,
        thread_id: str,
        role: str,
        content: str,
        content_type: str = "text",
        meta_data: Optional[Dict] = None,
        tool_calls: Optional[List] = None,
        attachments: Optional[List] = None
    ) -> Optional[ConversationMessage]:
        """
        添加会话消息

        Args:
            thread_id: 线程ID
            role: 消息角色 (human, ai, system, tool)
            content: 消息内容
            content_type: 内容类型
            meta_data: 消息元数据
            tool_calls: 工具调用信息
            attachments: 附件信息

        Returns:
            ConversationMessage: 创建的消息对象
        """
        try:
            async with self.db_session_factory() as session:
                # 获取当前消息序号
                count_query = text("""
                    SELECT COALESCE(MAX(message_index), -1) + 1
                    FROM conversation_messages
                    WHERE thread_id = :thread_id
                """)
                result = await session.execute(count_query, {"thread_id": thread_id})
                message_index = result.scalar()

                # 创建消息
                message = ConversationMessage(
                    id=str(uuid.uuid4()),
                    thread_id=thread_id,
                    role=role,
                    content=content,
                    content_type=content_type,
                    message_index=message_index,
                    meta_data=meta_data or {},
                    tool_calls=tool_calls,
                    attachments=attachments,
                    create_at=datetime.now()
                )

                session.add(message)

                # 更新会话的消息计数和最后消息时间
                update_query = text("""
                    UPDATE conversation_threads
                    SET message_count = message_count + 1,
                        last_message_at = :last_message_at,
                        update_at = :update_at
                    WHERE id = :thread_id
                """)

                await session.execute(update_query, {
                    "thread_id": thread_id,
                    "last_message_at": datetime.now(),
                    "update_at": datetime.now()
                })

                await session.commit()
                await session.refresh(message)

                # 添加到缓存
                if self.cache_service:
                    message_data = {
                        "id": message.id,
                        "role": message.role,
                        "content": message.content,
                        "content_type": message.content_type,
                        "message_index": message.message_index,
                        "meta_data": message.meta_data,
                        "create_at": message.create_at.isoformat()
                    }
                    await self.cache_service.add_session_message(thread_id, message_data)

                    # 标记为热点会话
                    await self.cache_service.mark_hot_session(thread_id)

                logger.debug(f"添加会话消息: {thread_id}, 角色: {role}, 序号: {message_index}")
                return message

        except Exception as e:
            logger.error(f"添加会话消息失败: {e}")
            return None

    async def get_conversation_messages(
        self,
        thread_id: str,
        user_id: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
        role_filter: Optional[str] = None
    ) -> List[Dict]:
        """
        获取会话消息列表

        Args:
            thread_id: 线程ID
            user_id: 用户ID (用于权限验证)
            limit: 限制数量
            offset: 偏移量
            role_filter: 角色过滤

        Returns:
            List[Dict]: 消息列表
        """
        try:
            # 先验证用户权限
            if user_id:
                conversation = await self.get_conversation(thread_id, user_id)
                if not conversation:
                    logger.warning(f"用户无权访问会话: {thread_id}, 用户: {user_id}")
                    return []

            # 尝试从缓存获取
            if self.cache_service and offset == 0 and limit <= 50 and not role_filter:
                cached_messages = await self.cache_service.get_session_messages(thread_id, limit)
                if cached_messages:
                    logger.debug(f"从缓存获取会话消息: {thread_id}, 数量: {len(cached_messages)}")
                    return cached_messages

            async with self.db_session_factory() as session:
                # 构建查询条件
                conditions = ["thread_id = :thread_id"]
                params = {"thread_id": thread_id, "limit": limit, "offset": offset}

                if role_filter:
                    conditions.append("role = :role_filter")
                    params["role_filter"] = role_filter

                where_clause = " AND ".join(conditions)

                query = text(f"""
                    SELECT id, role, content, content_type, message_index,
                           meta_data, tool_calls, attachments, create_at
                    FROM conversation_messages
                    WHERE {where_clause}
                    ORDER BY message_index ASC
                    LIMIT :limit OFFSET :offset
                """)

                result = await session.execute(query, params)
                rows = result.fetchall()

                # 格式化结果
                messages = []
                for row in rows:
                    message_data = {
                        "id": row.id,
                        "role": row.role,
                        "content": row.content,
                        "content_type": row.content_type,
                        "message_index": row.message_index,
                        "meta_data": row.meta_data or {},
                        "tool_calls": row.tool_calls,
                        "attachments": row.attachments,
                        "create_at": row.create_at.isoformat() if row.create_at else None
                    }
                    messages.append(message_data)

                # 缓存结果 (仅缓存完整的消息列表)
                if self.cache_service and offset == 0 and not role_filter:
                    for message in messages:
                        await self.cache_service.add_session_message(thread_id, message)

                logger.debug(f"获取会话消息: {thread_id}, 数量: {len(messages)}")
                return messages

        except Exception as e:
            logger.error(f"获取会话消息失败: {e}")
            return []

    # ==================== 会话搜索和统计 ====================

    async def search_conversations(
        self,
        user_id: str,
        query: str,
        agent_id: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict]:
        """
        搜索用户会话

        Args:
            user_id: 用户ID
            query: 搜索关键词
            agent_id: 智能体ID (可选)
            limit: 限制数量

        Returns:
            List[Dict]: 搜索结果
        """
        try:
            async with self.db_session_factory() as session:
                # 构建搜索条件
                conditions = ["user_id = :user_id", "status != 'deleted'"]
                params = {"user_id": user_id, "query": f"%{query}%", "limit": limit}

                if agent_id:
                    conditions.append("agent_id = :agent_id")
                    params["agent_id"] = agent_id

                where_clause = " AND ".join(conditions)

                # 在标题、描述中搜索
                search_query = text(f"""
                    SELECT id, agent_id, title, description, status,
                           create_at, update_at, message_count
                    FROM conversation_threads
                    WHERE {where_clause}
                    AND (title ILIKE :query OR description ILIKE :query)
                    ORDER BY update_at DESC
                    LIMIT :limit
                """)

                result = await session.execute(search_query, params)
                rows = result.fetchall()

                # 格式化结果
                conversations = []
                for row in rows:
                    conv_data = {
                        "id": row.id,
                        "agent_id": row.agent_id,
                        "title": row.title,
                        "description": row.description,
                        "status": row.status,
                        "create_at": row.create_at.isoformat() if row.create_at else None,
                        "update_at": row.update_at.isoformat() if row.update_at else None,
                        "message_count": row.message_count or 0
                    }
                    conversations.append(conv_data)

                logger.debug(f"搜索会话: {user_id}, 关键词: {query}, 结果数: {len(conversations)}")
                return conversations

        except Exception as e:
            logger.error(f"搜索会话失败: {e}")
            return []

    async def get_user_conversation_stats(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户会话统计信息

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            async with self.db_session_factory() as session:
                # 统计查询
                stats_query = text("""
                    SELECT
                        COUNT(*) as total_conversations,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_conversations,
                        COUNT(CASE WHEN status = 'archived' THEN 1 END) as archived_conversations,
                        SUM(message_count) as total_messages,
                        COUNT(DISTINCT agent_id) as agents_used,
                        MIN(create_at) as first_conversation_at,
                        MAX(update_at) as last_activity_at
                    FROM conversation_threads
                    WHERE user_id = :user_id AND status != 'deleted'
                """)

                result = await session.execute(stats_query, {"user_id": user_id})
                row = result.fetchone()

                stats = {
                    "total_conversations": row.total_conversations or 0,
                    "active_conversations": row.active_conversations or 0,
                    "archived_conversations": row.archived_conversations or 0,
                    "total_messages": row.total_messages or 0,
                    "agents_used": row.agents_used or 0,
                    "first_conversation_at": row.first_conversation_at.isoformat() if row.first_conversation_at else None,
                    "last_activity_at": row.last_activity_at.isoformat() if row.last_activity_at else None
                }

                # 按智能体统计
                agent_stats_query = text("""
                    SELECT agent_id, COUNT(*) as count, SUM(message_count) as messages
                    FROM conversation_threads
                    WHERE user_id = :user_id AND status != 'deleted'
                    GROUP BY agent_id
                    ORDER BY count DESC
                """)

                agent_result = await session.execute(agent_stats_query, {"user_id": user_id})
                agent_rows = agent_result.fetchall()

                stats["by_agent"] = [
                    {
                        "agent_id": row.agent_id,
                        "conversation_count": row.count,
                        "message_count": row.messages or 0
                    }
                    for row in agent_rows
                ]

                logger.debug(f"获取用户会话统计: {user_id}")
                return stats

        except Exception as e:
            logger.error(f"获取用户会话统计失败: {e}")
            return {}
