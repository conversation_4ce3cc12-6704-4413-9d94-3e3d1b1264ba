# 🚀 企业会话管理 API 使用指南

## 📋 概述

本指南提供了新的企业会话管理功能的详细使用示例，包括完整的 curl 命令和响应示例。

## 🔐 前置条件

### 1. 获取访问令牌
首先需要登录获取访问令牌：

```bash
# 用户登录
curl -X POST "http://localhost:8001/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=your_username&password=your_password"
```

响应示例：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user_id": 1,
  "username": "your_username",
  "role": "user"
}
```

### 2. 设置环境变量
```bash
# 设置访问令牌
export TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
export BASE_URL="http://localhost:8001"
```

## 🎯 核心功能使用示例

### 1. 智能体会话管理

#### 1.1 为智能体创建新会话
```bash
curl -X POST "$BASE_URL/api/agents/chatbot/conversations" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "技术咨询会话",
    "description": "关于AI技术的咨询对话"
  }'
```

响应示例：
```json
{
  "success": true,
  "thread_id": "550e8400-e29b-41d4-a716-446655440000",
  "agent_name": "chatbot",
  "title": "技术咨询会话",
  "description": "关于AI技术的咨询对话",
  "create_at": "2025-07-22T14:05:30.123456",
  "message": "会话创建成功"
}
```

#### 1.2 获取智能体会话列表
```bash
curl -X GET "$BASE_URL/api/agents/chatbot/conversations?page=1&page_size=10" \
  -H "Authorization: Bearer $TOKEN"
```

响应示例：
```json
{
  "success": true,
  "conversations": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "user_id": "1",
      "agent_id": "chatbot",
      "title": "技术咨询会话",
      "description": "关于AI技术的咨询对话",
      "status": "active",
      "create_at": "2025-07-22T14:05:30.123456",
      "update_at": "2025-07-22T14:05:30.123456",
      "last_message_at": null,
      "message_count": 0,
      "meta_data": {}
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "has_more": false,
  "agent_name": "chatbot"
}
```

#### 1.3 使用指定会话执行智能体对话
```bash
curl -X POST "$BASE_URL/api/agents/chat" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_name": "chatbot",
    "messages": [
      {
        "role": "user",
        "content": "你好，请介绍一下人工智能的发展历程"
      }
    ],
    "config": {
      "thread_id": "550e8400-e29b-41d4-a716-446655440000"
    },
    "stream": false
  }'
```

#### 1.4 自动创建会话的对话（不指定 thread_id）
```bash
curl -X POST "$BASE_URL/api/agents/chat" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_name": "chatbot",
    "messages": [
      {
        "role": "user", 
        "content": "你好，我想了解机器学习"
      }
    ],
    "stream": false
  }'
```

### 2. 通用会话管理

#### 2.1 创建新会话
```bash
curl -X POST "$BASE_URL/api/conversations/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "chatbot",
    "title": "深度学习讨论",
    "description": "关于深度学习技术的深入讨论",
    "meta_data": {
      "category": "技术",
      "priority": "high"
    }
  }'
```

#### 2.2 获取用户所有会话列表
```bash
curl -X GET "$BASE_URL/api/conversations/?page=1&page_size=20&order_by=update_at&order_desc=true" \
  -H "Authorization: Bearer $TOKEN"
```

#### 2.3 按智能体过滤会话
```bash
curl -X GET "$BASE_URL/api/conversations/?agent_id=chatbot&status=active" \
  -H "Authorization: Bearer $TOKEN"
```

#### 2.4 获取特定会话详情
```bash
curl -X GET "$BASE_URL/api/conversations/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer $TOKEN"
```

#### 2.5 更新会话信息
```bash
curl -X PUT "$BASE_URL/api/conversations/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "AI技术深度讨论",
    "description": "更新后的会话描述",
    "status": "active",
    "meta_data": {
      "category": "技术",
      "priority": "high",
      "updated": true
    }
  }'
```

#### 2.6 删除会话（软删除）
```bash
curl -X DELETE "$BASE_URL/api/conversations/550e8400-e29b-41d4-a716-446655440000?soft_delete=true" \
  -H "Authorization: Bearer $TOKEN"
```

### 3. 会话消息管理

#### 3.1 获取会话消息列表
```bash
curl -X GET "$BASE_URL/api/conversations/550e8400-e29b-41d4-a716-446655440000/messages?limit=50&offset=0" \
  -H "Authorization: Bearer $TOKEN"
```

响应示例：
```json
[
  {
    "id": "msg-001",
    "role": "human",
    "content": "你好，请介绍一下人工智能的发展历程",
    "content_type": "text",
    "message_index": 0,
    "meta_data": {},
    "create_at": "2025-07-22T14:10:00.123456"
  },
  {
    "id": "msg-002", 
    "role": "ai",
    "content": "人工智能的发展历程可以分为几个重要阶段...",
    "content_type": "text",
    "message_index": 1,
    "meta_data": {
      "agent_name": "chatbot",
      "model": "openai/gpt-4",
      "tools_used": 0
    },
    "create_at": "2025-07-22T14:10:05.123456"
  }
]
```

#### 3.2 按角色过滤消息
```bash
curl -X GET "$BASE_URL/api/conversations/550e8400-e29b-41d4-a716-446655440000/messages?role_filter=ai" \
  -H "Authorization: Bearer $TOKEN"
```

### 4. 会话搜索和统计

#### 4.1 搜索会话
```bash
curl -X GET "$BASE_URL/api/conversations/search?q=人工智能&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

#### 4.2 按智能体搜索
```bash
curl -X GET "$BASE_URL/api/conversations/search?q=技术&agent_id=chatbot" \
  -H "Authorization: Bearer $TOKEN"
```

#### 4.3 获取用户会话统计
```bash
curl -X GET "$BASE_URL/api/conversations/stats" \
  -H "Authorization: Bearer $TOKEN"
```

响应示例：
```json
{
  "total_conversations": 15,
  "active_conversations": 12,
  "archived_conversations": 3,
  "total_messages": 156,
  "agents_used": 2,
  "first_conversation_at": "2025-07-01T10:00:00.000000",
  "last_activity_at": "2025-07-22T14:10:00.000000",
  "by_agent": [
    {
      "agent_id": "chatbot",
      "conversation_count": 10,
      "message_count": 120
    },
    {
      "agent_id": "ReAct",
      "conversation_count": 5,
      "message_count": 36
    }
  ]
}
```

## 🌊 流式对话示例

### 流式智能体对话
```bash
curl -X POST "$BASE_URL/api/agents/chat" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_name": "chatbot",
    "messages": [
      {
        "role": "user",
        "content": "请详细解释深度学习的工作原理"
      }
    ],
    "config": {
      "thread_id": "550e8400-e29b-41d4-a716-446655440000"
    },
    "stream": true,
    "stream_mode": "values"
  }' \
  --no-buffer
```

流式响应示例：
```
data: {"status": "init", "meta": {"thread_id": "550e8400-e29b-41d4-a716-446655440000"}}

data: {"status": "streaming", "content": "深度学习是机器学习的一个分支"}

data: {"status": "streaming", "content": "，它模仿人脑神经网络的结构"}

data: {"status": "finished", "meta": {"thread_id": "550e8400-e29b-41d4-a716-446655440000"}}
```

## 🔧 高级用法

### 1. 批量操作示例

#### 获取多个智能体的会话统计
```bash
# 获取 chatbot 会话
curl -X GET "$BASE_URL/api/agents/chatbot/conversations" \
  -H "Authorization: Bearer $TOKEN" | jq '.total'

# 获取 ReAct 会话  
curl -X GET "$BASE_URL/api/agents/ReAct/conversations" \
  -H "Authorization: Bearer $TOKEN" | jq '.total'
```

### 2. 错误处理示例

#### 访问不存在的会话
```bash
curl -X GET "$BASE_URL/api/conversations/non-existent-id" \
  -H "Authorization: Bearer $TOKEN"
```

响应：
```json
{
  "detail": "会话不存在或无权限访问"
}
```

#### 无权限访问
```bash
curl -X GET "$BASE_URL/api/conversations/other-user-thread-id" \
  -H "Authorization: Bearer $TOKEN"
```

响应：
```json
{
  "detail": "会话不存在或无权限访问"
}
```

## 📊 最佳实践

### 1. 会话生命周期管理
```bash
# 1. 创建会话
THREAD_ID=$(curl -s -X POST "$BASE_URL/api/agents/chatbot/conversations" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "新会话"}' | jq -r '.thread_id')

# 2. 进行对话
curl -X POST "$BASE_URL/api/agents/chat" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"agent_name\": \"chatbot\",
    \"messages\": [{\"role\": \"user\", \"content\": \"你好\"}],
    \"config\": {\"thread_id\": \"$THREAD_ID\"}
  }"

# 3. 查看消息历史
curl -X GET "$BASE_URL/api/conversations/$THREAD_ID/messages" \
  -H "Authorization: Bearer $TOKEN"

# 4. 归档会话
curl -X PUT "$BASE_URL/api/conversations/$THREAD_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": "archived"}'
```

### 2. 分页查询大量会话
```bash
# 获取第一页
curl -X GET "$BASE_URL/api/conversations/?page=1&page_size=20" \
  -H "Authorization: Bearer $TOKEN"

# 获取下一页
curl -X GET "$BASE_URL/api/conversations/?page=2&page_size=20" \
  -H "Authorization: Bearer $TOKEN"
```

这些示例涵盖了企业会话管理系统的所有主要功能。您可以根据具体需求调整参数和请求内容。
