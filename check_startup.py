#!/usr/bin/env python3
"""
启动检查脚本

验证企业会话管理系统的修复是否成功
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_imports():
    """检查关键模块的导入"""
    print("🔍 检查模块导入...")
    
    try:
        # 检查数据模型
        print("  ✓ 检查数据模型...")
        from server.models.conversation_models import (
            ConversationThread, ConversationMessage, ConversationCheckpoint,
            ConversationSummary, UserConversationPreference, ConversationShare
        )
        print("    ✓ 会话数据模型导入成功")
        
        # 检查检查点存储器
        print("  ✓ 检查检查点存储器...")
        from src.agents.checkpointer.postgresql_saver import AsyncPostgreSQLSaver
        print("    ✓ PostgreSQL 检查点存储器导入成功")
        
        # 检查会话服务
        print("  ✓ 检查会话服务...")
        from src.services.conversation_service import ConversationService
        from src.services.conversation_cache import ConversationCacheService
        print("    ✓ 会话服务导入成功")
        
        # 检查路由器
        print("  ✓ 检查路由器...")
        from server.routers.conversation_router import conversation
        from server.routers.agents_router import agents
        print("    ✓ 路由器导入成功")
        
        # 检查智能体
        print("  ✓ 检查智能体...")
        from src.agents.chatbot.graph import ChatbotAgent
        print("    ✓ ChatbotAgent 导入成功")
        
        print("✅ 所有关键模块导入成功！")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def check_database_models():
    """检查数据库模型定义"""
    print("\n🔍 检查数据库模型...")

    try:
        from server.models.conversation_models import ConversationThread

        # 检查类属性而不是实例属性
        # 检查是否有 meta_data 字段而不是 metadata
        if hasattr(ConversationThread, 'meta_data'):
            print("    ✓ ConversationThread.meta_data 字段正确")
        else:
            print("    ❌ ConversationThread.meta_data 字段缺失")
            return False

        # 检查是否还有 metadata 字段（SQLAlchemy 保留字段除外）
        # SQLAlchemy 的 metadata 是类级别的，我们检查的是列级别的
        columns = [col.name for col in ConversationThread.__table__.columns]
        if 'metadata' in columns:
            print("    ❌ ConversationThread 表中仍有 metadata 列（应该是 meta_data）")
            return False

        if 'meta_data' in columns:
            print("    ✓ ConversationThread 表中有 meta_data 列")
        else:
            print("    ❌ ConversationThread 表中缺少 meta_data 列")
            return False

        print("✅ 数据库模型检查通过！")
        return True

    except Exception as e:
        print(f"❌ 数据库模型检查失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def check_sqlalchemy_compatibility():
    """检查 SQLAlchemy 兼容性（PostgreSQL）"""
    print("\n🔍 检查 SQLAlchemy 兼容性...")

    try:
        from server.models import Base
        from server.models.conversation_models import ConversationThread

        # 检查模型定义是否正确
        print("    ✓ 数据模型导入成功")

        # 检查字段定义
        if hasattr(ConversationThread, 'meta_data'):
            print("    ✓ ConversationThread.meta_data 字段存在")
        else:
            print("    ❌ ConversationThread.meta_data 字段缺失")
            return False

        # 检查表名
        if ConversationThread.__tablename__ == "conversation_threads":
            print("    ✓ 表名正确")
        else:
            print(f"    ❌ 表名错误: {ConversationThread.__tablename__}")
            return False

        print("✅ SQLAlchemy 兼容性检查通过！")
        return True

    except Exception as e:
        print(f"❌ SQLAlchemy 兼容性检查失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def check_api_routes():
    """检查 API 路由"""
    print("\n🔍 检查 API 路由...")
    
    try:
        from server.routers import router
        
        # 检查路由是否包含会话管理
        routes = [route.path for route in router.routes]
        
        conversation_routes = [r for r in routes if 'conversation' in r]
        if conversation_routes:
            print(f"    ✓ 发现会话管理路由: {len(conversation_routes)} 个")
            for route in conversation_routes[:3]:  # 显示前3个
                print(f"      - {route}")
        else:
            print("    ⚠️  未发现会话管理路由")
        
        print("✅ API 路由检查完成！")
        return True
        
    except Exception as e:
        print(f"❌ API 路由检查失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主检查函数"""
    print("🚀 企业会话管理系统启动检查")
    print("=" * 50)
    
    checks = [
        ("模块导入", check_imports),
        ("数据库模型", check_database_models),
        ("SQLAlchemy 兼容性", check_sqlalchemy_compatibility),
        ("API 路由", check_api_routes),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 检查出现异常: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("📊 检查结果汇总:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查都通过！企业会话管理系统修复成功！")
        print("\n📝 下一步:")
        print("  1. 运行数据库迁移脚本: migrations/create_conversation_tables.sql")
        print("  2. 启动应用程序: uvicorn server.main:app --reload")
        print("  3. 测试新的会话管理 API")
        return 0
    else:
        print("⚠️  部分检查未通过，请检查上述错误信息并修复。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
