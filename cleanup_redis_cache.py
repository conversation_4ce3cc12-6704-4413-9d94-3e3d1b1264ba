#!/usr/bin/env python3
"""
Redis缓存清理脚本
清理损坏的缓存键，解决UTF-8解码和JSON解析错误
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.adapters.redis import RedisAdapter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def cleanup_redis_cache():
    """清理Redis缓存中的损坏数据"""
    
    # Redis配置（可根据实际情况调整）
    redis_config = {
        'host': 'localhost',
        'port': 6379,
        'password': '',
        'db': 0,
        'key_prefix': '',
        'decode_responses': True
    }
    
    try:
        # 创建Redis适配器
        redis_adapter = RedisAdapter(redis_config, "cleanup")
        
        # 连接Redis
        connected = await redis_adapter.connect()
        if not connected:
            logger.error("无法连接到Redis，清理失败")
            return False
        
        logger.info("成功连接到Redis，开始清理损坏的缓存...")
        
        # 清理知识库相关缓存
        patterns_to_clean = [
            "KnowledgeRepository:*",  # 知识库缓存
            "user_kbs:*",             # 用户知识库列表缓存
            "user_tools:*",           # 用户工具缓存
            "*"                       # 所有缓存（最后执行）
        ]
        
        total_cleaned = 0
        for pattern in patterns_to_clean:
            logger.info(f"清理模式: {pattern}")
            try:
                cleaned_count = await redis_adapter.clean_corrupted_cache(pattern)
                total_cleaned += cleaned_count
                logger.info(f"模式 {pattern} 清理了 {cleaned_count} 个损坏的缓存键")
            except Exception as e:
                logger.error(f"清理模式 {pattern} 失败: {e}")
        
        logger.info(f"缓存清理完成，总共清理了 {total_cleaned} 个损坏的缓存键")
        
        # 断开连接
        await redis_adapter.disconnect()
        
        return total_cleaned > 0
        
    except Exception as e:
        logger.error(f"缓存清理过程中发生错误: {e}")
        return False

async def main():
    """主函数"""
    logger.info("开始Redis缓存清理...")
    
    success = await cleanup_redis_cache()
    
    if success:
        logger.info("Redis缓存清理成功完成")
        return 0
    else:
        logger.error("Redis缓存清理失败")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)