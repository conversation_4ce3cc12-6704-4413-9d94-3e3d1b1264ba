"""
会话缓存服务

基于 Redis 的会话数据缓存，提高会话访问性能
"""

import json
import pickle
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from src.utils import logger


class ConversationCacheService:
    """
    会话缓存服务
    
    提供会话数据的 Redis 缓存功能，包括：
    - 用户会话列表缓存
    - 热点会话数据缓存
    - 会话元数据缓存
    - 最近消息缓存
    """
    
    def __init__(self, redis_client):
        """
        初始化会话缓存服务
        
        Args:
            redis_client: Redis 客户端实例
        """
        self.redis = redis_client
        self.default_ttl = 3600  # 默认1小时过期
        self.user_sessions_ttl = 1800  # 用户会话列表30分钟过期
        self.hot_session_ttl = 7200  # 热点会话2小时过期
        
        # 缓存键前缀
        self.USER_SESSIONS_PREFIX = "user_sessions:"
        self.SESSION_META_PREFIX = "session_meta:"
        self.SESSION_MESSAGES_PREFIX = "session_messages:"
        self.SESSION_CHECKPOINT_PREFIX = "session_checkpoint:"
        self.HOT_SESSIONS_KEY = "hot_sessions"
        
        logger.info("会话缓存服务初始化完成")
    
    # ==================== 用户会话列表缓存 ====================
    
    async def get_user_sessions(self, user_id: str, agent_id: Optional[str] = None) -> Optional[List[Dict]]:
        """
        获取用户会话列表缓存
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID (可选)
            
        Returns:
            List[Dict]: 会话列表或 None
        """
        try:
            cache_key = f"{self.USER_SESSIONS_PREFIX}{user_id}"
            if agent_id:
                cache_key += f":{agent_id}"
            
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                sessions = json.loads(cached_data)
                logger.debug(f"从缓存获取用户会话列表: {user_id}, 数量: {len(sessions)}")
                return sessions
            
            return None
            
        except Exception as e:
            logger.error(f"获取用户会话列表缓存失败: {e}")
            return None
    
    async def set_user_sessions(self, user_id: str, sessions: List[Dict], agent_id: Optional[str] = None):
        """
        设置用户会话列表缓存
        
        Args:
            user_id: 用户ID
            sessions: 会话列表
            agent_id: 智能体ID (可选)
        """
        try:
            cache_key = f"{self.USER_SESSIONS_PREFIX}{user_id}"
            if agent_id:
                cache_key += f":{agent_id}"
            
            await self.redis.setex(
                cache_key,
                self.user_sessions_ttl,
                json.dumps(sessions, ensure_ascii=False, default=str)
            )
            
            logger.debug(f"设置用户会话列表缓存: {user_id}, 数量: {len(sessions)}")
            
        except Exception as e:
            logger.error(f"设置用户会话列表缓存失败: {e}")
    
    async def invalidate_user_sessions(self, user_id: str, agent_id: Optional[str] = None):
        """
        清除用户会话列表缓存
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID (可选)
        """
        try:
            cache_key = f"{self.USER_SESSIONS_PREFIX}{user_id}"
            if agent_id:
                cache_key += f":{agent_id}"
            else:
                # 清除所有相关的用户会话缓存
                pattern = f"{cache_key}*"
                keys = await self.redis.keys(pattern)
                if keys:
                    await self.redis.delete(*keys)
                    return
            
            await self.redis.delete(cache_key)
            logger.debug(f"清除用户会话列表缓存: {user_id}")
            
        except Exception as e:
            logger.error(f"清除用户会话列表缓存失败: {e}")
    
    # ==================== 会话元数据缓存 ====================
    
    async def get_session_meta(self, thread_id: str) -> Optional[Dict]:
        """
        获取会话元数据缓存
        
        Args:
            thread_id: 线程ID
            
        Returns:
            Dict: 会话元数据或 None
        """
        try:
            cache_key = f"{self.SESSION_META_PREFIX}{thread_id}"
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                meta = json.loads(cached_data)
                logger.debug(f"从缓存获取会话元数据: {thread_id}")
                return meta
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话元数据缓存失败: {e}")
            return None
    
    async def set_session_meta(self, thread_id: str, meta: Dict):
        """
        设置会话元数据缓存
        
        Args:
            thread_id: 线程ID
            meta: 会话元数据
        """
        try:
            cache_key = f"{self.SESSION_META_PREFIX}{thread_id}"
            await self.redis.setex(
                cache_key,
                self.default_ttl,
                json.dumps(meta, ensure_ascii=False, default=str)
            )
            
            logger.debug(f"设置会话元数据缓存: {thread_id}")
            
        except Exception as e:
            logger.error(f"设置会话元数据缓存失败: {e}")
    
    # ==================== 会话消息缓存 ====================
    
    async def get_session_messages(self, thread_id: str, limit: int = 50) -> Optional[List[Dict]]:
        """
        获取会话消息缓存
        
        Args:
            thread_id: 线程ID
            limit: 消息数量限制
            
        Returns:
            List[Dict]: 消息列表或 None
        """
        try:
            cache_key = f"{self.SESSION_MESSAGES_PREFIX}{thread_id}"
            
            # 使用 Redis List 存储消息，获取最新的 limit 条
            messages_data = await self.redis.lrange(cache_key, -limit, -1)
            
            if messages_data:
                messages = [json.loads(msg) for msg in messages_data]
                logger.debug(f"从缓存获取会话消息: {thread_id}, 数量: {len(messages)}")
                return messages
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话消息缓存失败: {e}")
            return None
    
    async def add_session_message(self, thread_id: str, message: Dict):
        """
        添加会话消息到缓存
        
        Args:
            thread_id: 线程ID
            message: 消息数据
        """
        try:
            cache_key = f"{self.SESSION_MESSAGES_PREFIX}{thread_id}"
            
            # 添加消息到 Redis List
            await self.redis.lpush(cache_key, json.dumps(message, ensure_ascii=False, default=str))
            
            # 限制消息数量，保留最新的100条
            await self.redis.ltrim(cache_key, 0, 99)
            
            # 设置过期时间
            await self.redis.expire(cache_key, self.default_ttl)
            
            logger.debug(f"添加会话消息到缓存: {thread_id}")
            
        except Exception as e:
            logger.error(f"添加会话消息到缓存失败: {e}")
    
    async def clear_session_messages(self, thread_id: str):
        """
        清除会话消息缓存
        
        Args:
            thread_id: 线程ID
        """
        try:
            cache_key = f"{self.SESSION_MESSAGES_PREFIX}{thread_id}"
            await self.redis.delete(cache_key)
            logger.debug(f"清除会话消息缓存: {thread_id}")
            
        except Exception as e:
            logger.error(f"清除会话消息缓存失败: {e}")
    
    # ==================== 检查点缓存 ====================
    
    async def get_session_checkpoint(self, thread_id: str, checkpoint_ns: str = "") -> Optional[Dict]:
        """
        获取会话检查点缓存
        
        Args:
            thread_id: 线程ID
            checkpoint_ns: 检查点命名空间
            
        Returns:
            Dict: 检查点数据或 None
        """
        try:
            cache_key = f"{self.SESSION_CHECKPOINT_PREFIX}{thread_id}:{checkpoint_ns}"
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                # 使用 pickle 序列化复杂对象
                checkpoint = pickle.loads(cached_data)
                logger.debug(f"从缓存获取会话检查点: {thread_id}")
                return checkpoint
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话检查点缓存失败: {e}")
            return None
    
    async def set_session_checkpoint(self, thread_id: str, checkpoint: Dict, checkpoint_ns: str = ""):
        """
        设置会话检查点缓存
        
        Args:
            thread_id: 线程ID
            checkpoint: 检查点数据
            checkpoint_ns: 检查点命名空间
        """
        try:
            cache_key = f"{self.SESSION_CHECKPOINT_PREFIX}{thread_id}:{checkpoint_ns}"
            
            # 使用 pickle 序列化复杂对象
            await self.redis.setex(
                cache_key,
                self.hot_session_ttl,
                pickle.dumps(checkpoint)
            )
            
            logger.debug(f"设置会话检查点缓存: {thread_id}")
            
        except Exception as e:
            logger.error(f"设置会话检查点缓存失败: {e}")
    
    # ==================== 热点会话管理 ====================
    
    async def mark_hot_session(self, thread_id: str, score: float = None):
        """
        标记热点会话
        
        Args:
            thread_id: 线程ID
            score: 热度分数 (默认使用当前时间戳)
        """
        try:
            if score is None:
                score = datetime.now().timestamp()
            
            await self.redis.zadd(self.HOT_SESSIONS_KEY, {thread_id: score})
            
            # 只保留最热的1000个会话
            await self.redis.zremrangebyrank(self.HOT_SESSIONS_KEY, 0, -1001)
            
            logger.debug(f"标记热点会话: {thread_id}, 分数: {score}")
            
        except Exception as e:
            logger.error(f"标记热点会话失败: {e}")
    
    async def get_hot_sessions(self, limit: int = 100) -> List[str]:
        """
        获取热点会话列表
        
        Args:
            limit: 数量限制
            
        Returns:
            List[str]: 热点会话ID列表
        """
        try:
            # 获取分数最高的会话
            hot_sessions = await self.redis.zrevrange(self.HOT_SESSIONS_KEY, 0, limit - 1)
            return [session.decode() if isinstance(session, bytes) else session for session in hot_sessions]
            
        except Exception as e:
            logger.error(f"获取热点会话列表失败: {e}")
            return []
    
    # ==================== 缓存统计和管理 ====================
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计数据
        """
        try:
            stats = {}
            
            # 统计各类缓存的数量
            user_sessions_count = len(await self.redis.keys(f"{self.USER_SESSIONS_PREFIX}*"))
            session_meta_count = len(await self.redis.keys(f"{self.SESSION_META_PREFIX}*"))
            session_messages_count = len(await self.redis.keys(f"{self.SESSION_MESSAGES_PREFIX}*"))
            checkpoint_count = len(await self.redis.keys(f"{self.SESSION_CHECKPOINT_PREFIX}*"))
            hot_sessions_count = await self.redis.zcard(self.HOT_SESSIONS_KEY)
            
            stats = {
                "user_sessions_cache": user_sessions_count,
                "session_meta_cache": session_meta_count,
                "session_messages_cache": session_messages_count,
                "checkpoint_cache": checkpoint_count,
                "hot_sessions": hot_sessions_count,
                "total_keys": sum([
                    user_sessions_count,
                    session_meta_count,
                    session_messages_count,
                    checkpoint_count
                ]),
                "timestamp": datetime.now().isoformat()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    async def clear_all_cache(self):
        """清除所有会话相关缓存"""
        try:
            patterns = [
                f"{self.USER_SESSIONS_PREFIX}*",
                f"{self.SESSION_META_PREFIX}*",
                f"{self.SESSION_MESSAGES_PREFIX}*",
                f"{self.SESSION_CHECKPOINT_PREFIX}*",
                self.HOT_SESSIONS_KEY
            ]
            
            for pattern in patterns:
                keys = await self.redis.keys(pattern)
                if keys:
                    await self.redis.delete(*keys)
            
            logger.info("清除所有会话缓存完成")
            
        except Exception as e:
            logger.error(f"清除所有会话缓存失败: {e}")
    
    async def clear_expired_cache(self):
        """清除过期的缓存数据"""
        try:
            # 清除过期的热点会话 (7天前的)
            expire_time = (datetime.now() - timedelta(days=7)).timestamp()
            await self.redis.zremrangebyscore(self.HOT_SESSIONS_KEY, 0, expire_time)
            
            logger.debug("清除过期缓存完成")
            
        except Exception as e:
            logger.error(f"清除过期缓存失败: {e}")
