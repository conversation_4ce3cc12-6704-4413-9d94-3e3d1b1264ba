# agents_router.py 修改说明

## 概述

已成功修改 `server/routers/agents_router.py` 文件，集成了新的企业会话管理系统，使用 PostgreSQL + Redis 架构替换原有的 SQLite 存储。

## 主要修改内容

### 1. 新增导入模块

```python
from src.services.conversation_service import ConversationService
from src.services.conversation_cache import ConversationCacheService
from src.database.manager import get_database_manager_dependency, UnifiedDatabaseManager
```

### 2. 添加会话服务依赖注入

新增 `get_conversation_service()` 函数，提供会话服务的依赖注入：

```python
async def get_conversation_service(
    db_manager: UnifiedDatabaseManager = Depends(get_database_manager_dependency)
) -> ConversationService:
    """获取会话服务实例"""
    # 获取 PostgreSQL 适配器
    # 创建数据库会话工厂
    # 获取 Redis 缓存服务
    # 返回 ConversationService 实例
```

### 3. 修改智能体执行函数

#### 3.1 函数签名更新
```python
async def execute_agent(
    request: AgentExecuteRequest,
    current_user: User = Depends(get_required_user),
    conversation_service: ConversationService = Depends(get_conversation_service)  # 新增
):
```

#### 3.2 会话管理逻辑
- **自动创建会话**: 当没有提供 `thread_id` 时，自动创建新会话
- **权限验证**: 验证用户是否有权限访问指定的会话
- **降级处理**: 当会话服务不可用时，使用临时 UUID

```python
# 处理会话管理
thread_id = config.get("thread_id")

if not thread_id:
    if conversation_service:
        # 创建新会话
        conversation = await conversation_service.create_conversation(
            user_id=current_user.id,
            agent_id=request.agent_name,
            title=f"与 {request.agent_name} 的对话"
        )
        thread_id = conversation.id
    else:
        thread_id = str(uuid.uuid4())
else:
    # 验证用户权限
    conversation = await conversation_service.get_conversation(thread_id, current_user.id)
    if not conversation:
        raise HTTPException(status_code=403, detail="无权限访问该会话")
```

#### 3.3 消息持久化
在非流式响应中添加消息保存逻辑：

```python
# 保存消息到会话
if conversation_service:
    # 保存用户消息
    await conversation_service.add_message(
        thread_id=thread_id,
        role="human",
        content=query,
        content_type="text"
    )
    
    # 保存AI响应
    await conversation_service.add_message(
        thread_id=thread_id,
        role="ai",
        content=response_content,
        content_type="text",
        metadata={
            "agent_name": request.agent_name,
            "model": f"{model_provider}/{model_name}",
            "tools_used": len(available_tools)
        }
    )
```

### 4. 修改流式响应处理

#### 4.1 函数签名更新
```python
async def _stream_agent_response(
    agent, messages, agent_config, meta: Dict[str, Any], 
    request: AgentExecuteRequest, current_user: User, 
    conversation_service: ConversationService = None  # 新增
):
```

#### 4.2 流式消息保存
在流式响应完成后保存用户消息：

```python
# 保存消息到会话 (流式响应完成后)
if conversation_service:
    user_message = messages[0].content if messages else ""
    await conversation_service.add_message(
        thread_id=meta["thread_id"],
        role="human",
        content=user_message,
        content_type="text"
    )
```

### 5. 新增会话管理 API 端点

#### 5.1 获取智能体会话列表
```python
@agents.get("/{agent_name}/conversations")
async def get_agent_conversations(
    agent_name: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_required_user),
    conversation_service: ConversationService = Depends(get_conversation_service)
):
```

#### 5.2 创建新会话
```python
@agents.post("/{agent_name}/conversations")
async def create_agent_conversation(
    agent_name: str,
    title: Optional[str] = Body(None),
    description: Optional[str] = Body(None),
    current_user: User = Depends(get_required_user),
    conversation_service: ConversationService = Depends(get_conversation_service)
):
```

## 核心特性

### 1. 企业级会话管理
- **PostgreSQL 存储**: 会话元数据和消息存储在 PostgreSQL 中
- **Redis 缓存**: 热点会话数据缓存，提高访问性能
- **权限控制**: 基于用户权限的会话访问控制
- **自动创建**: 智能体执行时自动创建和管理会话

### 2. 向下兼容
- **降级处理**: 当会话服务不可用时，自动降级到临时 UUID 模式
- **API 兼容**: 保持原有 API 接口的兼容性
- **渐进式升级**: 可以逐步启用新的会话管理功能

### 3. 性能优化
- **缓存策略**: Redis 缓存用户会话列表和热点会话数据
- **分页支持**: 支持大量会话的分页查询
- **异步处理**: 全异步的数据库操作

### 4. 监控和日志
- **详细日志**: 记录会话创建、访问、权限验证等关键操作
- **错误处理**: 完善的异常处理和错误恢复机制
- **性能监控**: 支持会话访问性能监控

## 使用示例

### 1. 创建新会话并执行智能体
```bash
# 创建新会话
POST /api/agents/chatbot/conversations
{
    "title": "技术咨询会话",
    "description": "关于AI技术的咨询对话"
}

# 使用会话执行智能体
POST /api/agents/chat
{
    "agent_name": "chatbot",
    "messages": [{"role": "user", "content": "你好"}],
    "config": {"thread_id": "返回的会话ID"}
}
```

### 2. 获取会话列表
```bash
GET /api/agents/chatbot/conversations?page=1&page_size=20
```

### 3. 自动会话管理
```bash
# 不提供 thread_id，系统自动创建新会话
POST /api/agents/chat
{
    "agent_name": "chatbot",
    "messages": [{"role": "user", "content": "你好"}]
}
```

## 技术优势

1. **企业级架构**: 基于 PostgreSQL + Redis 的高可用架构
2. **权限感知**: 完整的用户权限验证和数据隔离
3. **高性能**: Redis 缓存和异步处理提供优秀性能
4. **可扩展**: 支持大规模用户和会话管理
5. **可维护**: 清晰的代码结构和完善的错误处理

## 注意事项

1. **流式响应限制**: 在流式响应中，AI 消息的完整内容需要在客户端完成后单独保存
2. **服务依赖**: 依赖 PostgreSQL 和 Redis 服务的可用性
3. **权限验证**: 所有会话操作都会进行用户权限验证
4. **缓存一致性**: 需要注意 Redis 缓存与数据库数据的一致性

这些修改使得 `agents_router.py` 完全集成了新的企业会话管理系统，提供了完整的会话生命周期管理功能。
